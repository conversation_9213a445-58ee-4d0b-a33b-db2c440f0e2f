import { useState, useEffect, useRef, useCallback } from 'react';
import { BaseStation, PolarEarthProps } from '../../../types/type';
import { BeamCellManager } from '../../../features/beamCell';
import { setupMouseHandlers } from '../../../features/mouse';
import { earthRotate, wgs84ToCartesign, GetWGS84FromDKR } from '../../../utils/setfunction';
import useSimulationStore from '../../../../store/simulationStore';

// Cesium 是全局变量，通过 public/index.html 中的 script 标签加载
declare const Cesium: any;

export interface CesiumViewerState {
  viewer: any;
  isInit: boolean;
  isRotate: boolean;
  satelliteList: string[];
  baseStationList: BaseStation[];
  curSatellite: string;
  curBaseStation: BaseStation | null;
  polarPosition: PolarEarthProps | null;
  beamCellManager: React.MutableRefObject<BeamCellManager | null>;
  nowPicksatellite: React.MutableRefObject<any>;
  selectedSatelliteEntity: React.MutableRefObject<any>;
}

export interface CesiumViewerActions {
  setIsRotate: (rotate: boolean) => void;
  setCurSatellite: (satellite: string) => void;
  setCurBaseStation: (station: BaseStation | null) => void;
  setSatelliteList: (list: string[]) => void;
  setBaseStationList: (list: BaseStation[]) => void;
  initializeCesium: () => void;
  cleanupCesium: () => void;
  wgs84ToCartesignWrapper: (lng: any, lat: any, alt: any) => any;
  GetWGS84FromDKRWrapper: (coor: any, type: number) => any;
}

/**
 * Cesium 3D视图管理Hook
 * 负责Cesium初始化、事件处理、资源管理
 */
export const useCesiumViewer = (): CesiumViewerState & CesiumViewerActions => {
  // 状态管理
  const [isInit, setIsInit] = useState<boolean>(false);
  const [isRotate, setIsRotate] = useState<boolean>(false);
  const [satelliteList, setSatelliteList] = useState<string[]>([]);
  const [baseStationList, setBaseStationList] = useState<BaseStation[]>([]);
  const [curSatellite, setCurSatellite] = useState<string>("");
  const [curBaseStation, setCurBaseStation] = useState<BaseStation | null>(null);
  const [polarPosition, setPolarPosition] = useState<PolarEarthProps | null>(null);

  // Refs
  const viewerRef = useRef<any>(null);
  const satelliteListRef = useRef(satelliteList);
  const curBaseStationRef = useRef(curBaseStation);
  const beamCellManager = useRef<BeamCellManager | null>(null);
  const nowPicksatellite = useRef<any>(null);
  const selectedSatelliteEntity = useRef<any>(null);
  const handlerRef = useRef<any>(null);
  const previousTimeRef = useRef<any>(null);

  // Zustand store
  const { setPickedObject } = useSimulationStore();

  // 更新refs
  useEffect(() => {
    satelliteListRef.current = satelliteList;
  }, [satelliteList]);

  useEffect(() => {
    curBaseStationRef.current = curBaseStation;
  }, [curBaseStation]);

  // 地球旋转函数
  const earthRotateWrapper = useCallback(() => {
    if (viewerRef.current && previousTimeRef.current !== undefined) {
      const earthRotateParams = {
        viewer: viewerRef.current,
        previousTime: { current: previousTimeRef.current }
      };
      earthRotate(earthRotateParams);
      previousTimeRef.current = earthRotateParams.previousTime.current;
    }
  }, []);

  // 监听旋转状态变化
  useEffect(() => {
    if (viewerRef.current) {
      if (isRotate) {
        viewerRef.current.clock.onTick.addEventListener(earthRotateWrapper);
      } else {
        viewerRef.current.clock.onTick.removeEventListener(earthRotateWrapper);
      }
    }
  }, [isRotate, earthRotateWrapper]);

  // 坐标转换函数
  const wgs84ToCartesignWrapper = useCallback((lng: any, lat: any, alt: any) => {
    return wgs84ToCartesign(lng, lat, alt, viewerRef.current);
  }, []);

  const GetWGS84FromDKRWrapper = useCallback((coor: any, type: number) => {
    return GetWGS84FromDKR(coor, type);
  }, []);

  // Cesium初始化
  const initializeCesium = useCallback(() => {
    if (isInit || viewerRef.current) return;

    console.log('Cesium初始化开始');
    
    try {
      // 设置Cesium Ion token
      Cesium.Ion.defaultAccessToken = 
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJiYTg4MTUyNy0zMTA2LTRiMDktOGE1My05ZDA4OTRmOTE3YzciLCJpZCI6MTAzMjg1LCJpYXQiOjE2NTk0MDcyODB9.sfpT8e4oxun23JG--UmUN9ZD4SbQfU-Ljvh2MsPTTcY";
      
      // 创建Cesium Viewer
      viewerRef.current = new Cesium.Viewer("cesiumContainer", {
        shouldAnimate: true,
        infoBox: false,
        orderIndependentTranslucency: true,
        contextOptions: {
          webgl: { alpha: true }
        },
        timeline: true,
        animation: true,
      });

      const viewer = viewerRef.current;

      // 基础设置
      viewer.scene.skyBox.show = false;
      viewer.scene.sun.show = true;
      viewer.scene.moon.show = true;
      viewer.clock.shouldAnimate = true;
      previousTimeRef.current = viewer.clock.currentTime.secondsOfDay;

      // 分辨率优化
      if (Cesium.FeatureDetection.supportsImageRenderingPixelated()) {
        let vtxf_dpr = window.devicePixelRatio;
        while (vtxf_dpr >= 2.0) {
          vtxf_dpr /= 2.0;
        }
        viewer.resolutionScale = vtxf_dpr;
      }

      // 初始化波束小区管理器
      beamCellManager.current = new BeamCellManager({ viewer });

      // 设置鼠标事件处理器
      const mouseHandlerDependencies = {
        viewer,
        setCurSatellite,
        setNowSystemDate: () => {}, // 临时空函数，需要从外部传入
        setSatellitePostionData: () => {}, // 临时空函数，需要从外部传入
        wgs84ToCartesignWrapper,
        nowPicksatellite,
        setPickedObject,
        beamCellManager,
        selectedSatelliteEntity
      };
      
      handlerRef.current = setupMouseHandlers(mouseHandlerDependencies);

      // 设置初始视角
      viewer.camera.setView({
        destination: Cesium.Cartesian3.fromDegrees(0, 0, 20000000),
        orientation: {
          heading: 0.0,
          pitch: -Cesium.Math.PI_OVER_TWO,
          roll: 0.0
        }
      });

      // Home按钮事件
      viewer.homeButton.viewModel.command.afterExecute.addEventListener(() => {
        setCurBaseStation(null);
        if (viewer.scene.mode !== Cesium.SceneMode.SCENE3D) {
          viewer.scene.mode = Cesium.SceneMode.SCENE3D;
        }
        viewer.camera.setView({
          destination: Cesium.Cartesian3.fromDegrees(0, 0, 20000000),
          orientation: {
            heading: 0.0,
            pitch: -Cesium.Math.PI_OVER_TWO,
            roll: 0.0
          }
        });
        setIsRotate(false);
      });

      setIsInit(true);
      console.log('Cesium初始化完成');
      
    } catch (error) {
      console.error('Cesium初始化失败:', error);
    }
  }, [isInit, setCurSatellite, setPickedObject, wgs84ToCartesignWrapper]);

  // 清理Cesium资源
  const cleanupCesium = useCallback(() => {
    if (viewerRef.current) {
      // 移除事件监听器
      if (isRotate) {
        viewerRef.current.clock.onTick.removeEventListener(earthRotateWrapper);
      }
      
      // 清理鼠标处理器
      if (handlerRef.current && handlerRef.current.destroy) {
        handlerRef.current.destroy();
      }
      
      // 清理波束小区管理器
      if (beamCellManager.current) {
        beamCellManager.current = null;
      }
      
      // 销毁viewer
      viewerRef.current.destroy();
      viewerRef.current = null;
    }
    
    setIsInit(false);
  }, [isRotate, earthRotateWrapper]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      cleanupCesium();
    };
  }, [cleanupCesium]);

  return {
    // 状态
    viewer: viewerRef.current,
    isInit,
    isRotate,
    satelliteList,
    baseStationList,
    curSatellite,
    curBaseStation,
    polarPosition,
    beamCellManager,
    nowPicksatellite,
    selectedSatelliteEntity,
    
    // 操作
    setIsRotate,
    setCurSatellite,
    setCurBaseStation,
    setSatelliteList,
    setBaseStationList,
    initializeCesium,
    cleanupCesium,
    wgs84ToCartesignWrapper,
    GetWGS84FromDKRWrapper,
  };
};
