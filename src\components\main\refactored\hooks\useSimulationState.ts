// @ts-nocheck
import { useState, useEffect, useRef, useCallback } from 'react';
import { situationType, SettingType, SceneType } from '../../../types/type';
import useSimulationStore from '../../../../store/simulationStore';
import { postCurrentTime } from '../../../utils/api/postAPI';

export interface SimulationState {
  situation: situationType;
  setting: SettingType;
  sceneList: SceneType[];
  nowSystemDate: string[];
  satellitePostionData: number[];
  satelliteColor: {};
  weatherIcon: string;
  isShowNet: boolean;
  isShowBasestationNet: boolean;
  groundBusinessState: any;
  groundReliabilityState: any;
  groundStabilityState: any;
}

export interface SimulationActions {
  setSituation: (situation: situationType) => void;
  setSetting: (setting: SettingType) => void;
  setSceneList: (scenes: SceneType[]) => void;
  setNowSystemDate: (dates: string[]) => void;
  setSatellitePostionData: (data: number[]) => void;
  setSatelliteColor: (colors: {}) => void;
  setWeatherIcon: (icon: string) => void;
  setIsShowNet: (show: boolean) => void;
  setIsShowBasestationNet: (show: boolean) => void;
  updateCesiumTimeToStore: () => string | undefined;
}

/**
 * 仿真状态管理Hook
 * 负责仿真相关的状态管理和时间同步
 */
export const useSimulationState = (): SimulationState & SimulationActions & {
  // Zustand store状态
  simulationRunning: boolean;
  isLoading: boolean;
  simulationConstellationName: string;
  currentSatelliteName: string;
  cesiumTime: string;
  timeDiff: number;
  networkModeConfig: any;
  kvmList: any[];
} => {
  // 本地状态
  const [situation, setSituation] = useState<situationType>({
    satellite: true,
    communicate: true,
    basestation: false,
    resource: false,
    business: false,
    current: ""
  });

  const [setting, setSetting] = useState<SettingType>({
    label: { val: false, name: "卫星标注" },
    icon: { val: true, name: "卫星图标" },
    track: { val: false, name: "卫星轨迹" },
    light: { val: false, name: "显示光照" },
    sun: { val: true, name: "显示太阳" },
    star: { val: false, name: "显示星空" },
    time: { val: true, name: "显示时间轴" },
    rotate: { val: false, name: "地球旋转" },
  });

  const [sceneList, setSceneList] = useState<SceneType[]>([]);
  const [nowSystemDate, setNowSystemDate] = useState<string[]>([]);
  const [satellitePostionData, setSatellitePostionData] = useState<number[]>([]);
  const [satelliteColor, setSatelliteColor] = useState({});
  const [weatherIcon, setWeatherIcon] = useState<string>("0");
  const [isShowNet, setIsShowNet] = useState<boolean>(false);
  const [isShowBasestationNet, setIsShowBasestationNet] = useState<boolean>(false);
  const [groundBusinessState, setGroundBusinessState] = useState<any>(null);
  const [groundReliabilityState, setGroundReliabilityState] = useState<any>(null);
  const [groundStabilityState, setGroundStabilityState] = useState<any>(null);

  // Zustand store
  const {
    simulationRunning,
    isLoading,
    simulationConstellationName,
    currentSatelliteName,
    cesiumTime,
    timeDiff,
    networkModeConfig,
    kvmList,
    setCesiumTime,
    setTimeDiff,
  } = useSimulationStore();

  // 定时器引用
  const timeIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // 更新Cesium时间到store的函数
  const updateCesiumTimeToStore = useCallback((viewer?: any) => {
    if (!viewer || !viewer.clock) {
      return undefined;
    }

    try {
      const cesiumTime = viewer.clock.currentTime.toString();

      // 防护性检查
      if (!viewer.clock.currentTime || !viewer.clock.startTime) {
        console.warn('时钟时间无效，跳过时间差计算');
        return cesiumTime;
      }

      // 计算时间差
      const currentTime = viewer.clock.currentTime;
      const startTime = viewer.clock.startTime;
      const secondsDiff = Cesium.JulianDate.secondsDifference(currentTime, startTime);

      if (isNaN(secondsDiff) || !isFinite(secondsDiff)) {
        console.warn('时间差计算结果无效:', secondsDiff);
        return cesiumTime;
      }

      // 转换为纳秒
      const diff = Math.round(secondsDiff * 1000000000);
      setTimeDiff(diff);
      setCesiumTime(cesiumTime);

      return cesiumTime;
    } catch (error) {
      console.error('更新Cesium时间失败:', error);
      return undefined;
    }
  }, [setCesiumTime, setTimeDiff]);

  // 启动时间同步定时器
  const startTimeSync = useCallback((viewer: any) => {
    if (timeIntervalRef.current) {
      clearInterval(timeIntervalRef.current);
    }

    timeIntervalRef.current = setInterval(() => {
      try {
        if (!viewer || !viewer.clock || !viewer.clock.currentTime || !viewer.clock.startTime) {
          console.warn("时钟对象不存在，跳过本次执行");
          return;
        }

        const currentTime = viewer.clock.currentTime;
        const startTime = viewer.clock.startTime;
        const secondsDiff = Cesium.JulianDate.secondsDifference(currentTime, startTime);

        if (isNaN(secondsDiff) || !isFinite(secondsDiff)) {
          console.warn("时间差计算结果无效，跳过本次执行");
          return;
        }

        const diff = Math.round(secondsDiff * 1000000000);
        
        postCurrentTime(diff)
          .then(() => {
            // console.log("成功发送当前时间");
          })
          .catch((error) => {
            console.error("发送当前时间失败:", error);
          });
      } catch (error) {
        console.error("定时器执行过程中发生错误:", error);
      }
    }, 500);
  }, []);

  // 停止时间同步定时器
  const stopTimeSync = useCallback(() => {
    if (timeIntervalRef.current) {
      clearInterval(timeIntervalRef.current);
      timeIntervalRef.current = null;
    }
  }, []);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      stopTimeSync();
    };
  }, [stopTimeSync]);

  // 监听situation变化，控制Cesium容器显示
  useEffect(() => {
    const cesiumContainer = document.getElementById("cesiumContainer");
    if (cesiumContainer) {
      if (situation.resource || situation.business) {
        cesiumContainer.style.display = "none";
      } else {
        cesiumContainer.style.display = "block";
      }
    }
  }, [situation]);

  return {
    // 本地状态
    situation,
    setting,
    sceneList,
    nowSystemDate,
    satellitePostionData,
    satelliteColor,
    weatherIcon,
    isShowNet,
    isShowBasestationNet,
    groundBusinessState,
    groundReliabilityState,
    groundStabilityState,

    // 本地操作
    setSituation,
    setSetting,
    setSceneList,
    setNowSystemDate,
    setSatellitePostionData,
    setSatelliteColor,
    setWeatherIcon,
    setIsShowNet,
    setIsShowBasestationNet,
    updateCesiumTimeToStore,

    // Zustand store状态
    simulationRunning,
    isLoading,
    simulationConstellationName,
    currentSatelliteName,
    cesiumTime,
    timeDiff,
    networkModeConfig,
    kvmList,

    // 时间同步控制
    startTimeSync,
    stopTimeSync,
  } as any;
};
