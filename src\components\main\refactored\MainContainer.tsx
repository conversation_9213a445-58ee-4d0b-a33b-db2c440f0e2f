import React from 'react';
import { useCesiumViewer, useSimulationState, useModalStates } from './hooks';
import {
  CesiumViewport,
  HeaderSection,
  LeftPanel,
  RightPanel,
  LoadingOverlay,
  ModalsContainer
} from './components';

interface MainContainerProps {
  setDashboard?: (dashboard: any) => void;
}

/**
 * 主容器组件 - 重构后的main.tsx
 * 负责整体布局和状态管理的协调
 */
const MainContainer: React.FC<MainContainerProps> = (props) => {
  const { setDashboard } = props;

  // 使用自定义Hook管理Cesium相关状态
  const cesiumState = useCesiumViewer();
  
  // 使用自定义Hook管理仿真状态
  const simulationState = useSimulationState();
  
  // 使用自定义Hook管理弹窗状态
  const modalStates = useModalStates();

  return (
    <>
      {/* 头部导航 */}
      <HeaderSection 
        modalStates={modalStates}
        simulationState={simulationState}
      />

      {/* Cesium 3D视图容器 */}
      <CesiumViewport 
        cesiumState={cesiumState}
        simulationState={simulationState}
      />

      {/* 左侧面板 */}
      {(simulationState.situation.satellite || simulationState.situation.communicate) && (
        <LeftPanel 
          cesiumState={cesiumState}
          simulationState={simulationState}
        />
      )}

      {/* 右侧面板 */}
      {(simulationState.situation.satellite || simulationState.situation.communicate) && (
        <RightPanel 
          cesiumState={cesiumState}
          simulationState={simulationState}
        />
      )}

      {/* 加载动画 */}
      <LoadingOverlay isLoading={simulationState.isLoading} />

      {/* 所有弹窗容器 */}
      <ModalsContainer 
        modalStates={modalStates}
        cesiumState={cesiumState}
        simulationState={simulationState}
      />
    </>
  );
};

export default MainContainer;
