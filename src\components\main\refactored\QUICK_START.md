# 快速开始指南

## 🚀 5分钟快速体验

### 步骤1：导入测试组件
```tsx
// 在您的App.tsx或其他组件中
import { TestMain } from './components/main/refactored';

function App() {
  return (
    <div className="App">
      <TestMain />
    </div>
  );
}
```

### 步骤2：运行项目
```bash
npm start
```

### 步骤3：查看效果
- 🌍 Cesium 3D地球视图
- 📊 实时状态显示面板
- 🎮 地球旋转控制按钮
- ✨ 完整的TypeScript类型支持

## 📋 完整替换原始main.tsx

### 替换步骤
```tsx
// 原来的用法
import CesiumComponent from './components/main/main';

// 替换为
import { MainContainer } from './components/main/refactored';

// 使用方式
<MainContainer setDashboard={handleDashboard} />
```

## 🔧 自定义开发

### 使用单个Hook
```tsx
import React, { useEffect } from 'react';
import { useCesiumViewer } from './components/main/refactored/hooks';

// 必须添加Cesium声明
declare const Cesium: any;

const MyComponent = () => {
  const cesiumState = useCesiumViewer();
  
  useEffect(() => {
    if (!cesiumState.isInit) {
      cesiumState.initializeCesium();
    }
  }, [cesiumState.isInit, cesiumState.initializeCesium]);

  return (
    <div>
      <div id="cesiumContainer" style={{ width: '100%', height: '400px' }} />
      <p>状态: {cesiumState.isInit ? '已初始化' : '未初始化'}</p>
    </div>
  );
};
```

### 使用多个Hook
```tsx
import React from 'react';
import { 
  useCesiumViewer, 
  useSimulationState, 
  useModalStates 
} from './components/main/refactored/hooks';

// 必须添加Cesium声明
declare const Cesium: any;

const AdvancedComponent = () => {
  const cesiumState = useCesiumViewer();
  const simulationState = useSimulationState();
  const modalStates = useModalStates();

  return (
    <div>
      {/* 您的自定义UI */}
      <button onClick={modalStates.showConstellationSettingPanel}>
        打开星座配置
      </button>
    </div>
  );
};
```

## ⚠️ 重要提醒

### 必须添加Cesium声明
在每个使用Cesium的文件中添加：
```tsx
// Cesium 是全局变量，通过 public/index.html 中的 script 标签加载
declare const Cesium: any;
```

### 为什么需要这个声明？
- ✅ 保持TypeScript类型安全
- ✅ 获得IDE智能提示
- ✅ 及早发现潜在错误
- ✅ 更好的代码质量

## 🛠️ 故障排除

### 问题1：Cesium is not defined
**解决方案**：确保 `public/index.html` 包含：
```html
<script src="./Cesium/Cesium.js"></script>
```

### 问题2：TypeScript错误
**解决方案**：在文件顶部添加：
```tsx
declare const Cesium: any;
```

### 问题3：导入错误
**解决方案**：使用正确的导入路径：
```tsx
import { TestMain, MainContainer } from './components/main/refactored';
```

## 📈 性能优化

### 使用性能工具
```tsx
import { 
  ResourceManager, 
  PerformanceMonitor 
} from './components/main/refactored/utils/performanceUtils';

// 资源管理
const resourceManager = new ResourceManager();
resourceManager.register('my-resource', () => cleanup());

// 性能监控
const perfMonitor = new PerformanceMonitor();
perfMonitor.mark('start');
// ... 您的代码
perfMonitor.measure('操作耗时', 'start');
```

## 🎯 下一步

1. **测试基本功能**：使用 `TestMain` 验证环境
2. **逐步迁移**：将原始main.tsx替换为 `MainContainer`
3. **自定义开发**：使用Hooks构建自己的组件
4. **性能优化**：使用内置的性能工具
5. **类型完善**：根据需要添加更详细的类型定义

## 📚 更多资源

- [完整文档](./README.md)
- [使用示例](./USAGE_EXAMPLE.md)
- [类型安全演示](./TYPE_SAFETY_DEMO.md)
- [性能优化工具](./utils/performanceUtils.ts)

开始您的重构之旅吧！🚀
