# 使用示例

## 快速开始

### 1. 使用测试组件（推荐）

```tsx
// 在 App.tsx 或其他地方使用
import { TestMain } from './components/main/refactored';

function App() {
  return (
    <div className="App">
      <TestMain />
    </div>
  );
}
```

### 2. 使用完整的主容器组件

```tsx
// 替换原始的main.tsx
import { MainContainer } from './components/main/refactored';

function App() {
  const handleDashboardChange = (dashboard: any) => {
    console.log('Dashboard changed:', dashboard);
  };

  return (
    <div className="App">
      <MainContainer setDashboard={handleDashboardChange} />
    </div>
  );
}
```

### 3. 单独使用Hooks

```tsx
import React, { useEffect } from 'react';
import { useCesiumViewer, useSimulationState } from './components/main/refactored/hooks';

// Cesium 是全局变量
declare const Cesium: any;

const MyCustomComponent: React.FC = () => {
  const cesiumState = useCesiumViewer();
  const simulationState = useSimulationState();

  useEffect(() => {
    // 初始化Cesium
    if (!cesiumState.isInit) {
      cesiumState.initializeCesium();
    }
  }, [cesiumState.isInit, cesiumState.initializeCesium]);

  return (
    <div>
      <div id="cesiumContainer" style={{ width: '100%', height: '500px' }} />
      <div>
        <p>Cesium状态: {cesiumState.isInit ? '已初始化' : '未初始化'}</p>
        <p>卫星数量: {cesiumState.satelliteList.length}</p>
        <button onClick={() => cesiumState.setIsRotate(!cesiumState.isRotate)}>
          {cesiumState.isRotate ? '停止旋转' : '开始旋转'}
        </button>
      </div>
    </div>
  );
};
```

## 重要说明

### Cesium全局变量

项目中的Cesium是通过HTML script标签加载的全局变量，不需要import。在TypeScript文件中使用时，只需要添加声明：

```tsx
// Cesium 是全局变量，通过 public/index.html 中的 script 标签加载
declare const Cesium: any;
```

### 与原始main.tsx的对比

| 特性 | 原始main.tsx | 重构后 |
|------|-------------|--------|
| 文件大小 | 989行 | 拆分为多个小文件 |
| TypeScript | 使用@ts-nocheck | 完整类型支持 |
| 可维护性 | 低 | 高 |
| 可复用性 | 低 | 高 |
| 性能优化 | 无 | 内置多种优化工具 |
| 测试友好 | 难以测试 | 易于单元测试 |

### 迁移步骤

1. **测试阶段**：先使用 `TestMain` 组件验证基本功能
2. **逐步替换**：将原始main.tsx的使用替换为 `MainContainer`
3. **功能验证**：确保所有功能正常工作
4. **性能优化**：根据需要使用性能优化工具

### 故障排除

#### 1. Cesium未定义错误
确保 `public/index.html` 中包含Cesium的script标签：
```html
<script src="./Cesium/Cesium.js"></script>
```

#### 2. 模块导入错误
使用统一的导入方式：
```tsx
import { MainContainer, TestMain, useCesiumViewer } from './components/main/refactored';
```

#### 3. TypeScript类型错误
在使用Cesium的文件中添加声明：
```tsx
declare const Cesium: any;
```

## 性能优化建议

### 1. 使用性能监控工具

```tsx
import { PerformanceMonitor } from './components/main/refactored/utils/performanceUtils';

const perfMonitor = new PerformanceMonitor();
perfMonitor.mark('init-start');
// ... 初始化代码
perfMonitor.measure('初始化耗时', 'init-start');
```

### 2. 使用资源管理器

```tsx
import { ResourceManager } from './components/main/refactored/utils/performanceUtils';

const resourceManager = new ResourceManager();
resourceManager.register('cesium-viewer', () => viewer.destroy());
// 组件卸载时自动清理
```

### 3. 使用防抖节流

```tsx
import { debounce, throttle } from './components/main/refactored/utils/performanceUtils';

const debouncedResize = debounce(handleResize, 300);
const throttledUpdate = throttle(updateData, 100);
```
