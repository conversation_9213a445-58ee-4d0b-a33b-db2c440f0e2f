// @ts-nocheck
import React, { useEffect } from 'react';
import { useCesiumViewer } from './hooks/useCesiumViewer';
import { useSimulationState } from './hooks/useSimulationState';

interface TestMainProps {
  setDashboard?: (dashboard: any) => void;
}

/**
 * 测试重构后的主组件
 * 验证Hooks是否能正常工作
 */
const TestMain: React.FC<TestMainProps> = (props) => {
  const { setDashboard } = props;

  // 使用重构后的Hooks
  const cesiumState = useCesiumViewer();
  const simulationState = useSimulationState();

  // 初始化Cesium
  useEffect(() => {
    if (!cesiumState.isInit) {
      cesiumState.initializeCesium();
    }
    
    return () => {
      cesiumState.cleanupCesium();
    };
  }, [cesiumState.isInit, cesiumState.initializeCesium, cesiumState.cleanupCesium]);

  return (
    <div style={{ width: '100%', height: '100vh', position: 'relative' }}>
      {/* Cesium 3D视图容器 */}
      <div
        id="cesiumContainer"
        style={{
          height: "100%",
          width: "100%",
          background: "#000",
        }}
      />

      {/* 状态显示 */}
      <div style={{
        position: 'absolute',
        top: '10px',
        left: '10px',
        background: 'rgba(0,0,0,0.8)',
        color: 'white',
        padding: '15px',
        borderRadius: '8px',
        fontSize: '14px',
        zIndex: 1000,
        minWidth: '250px'
      }}>
        <h3 style={{ margin: '0 0 10px 0', color: '#4CAF50' }}>重构测试</h3>
        <div>Cesium初始化: {cesiumState.isInit ? '✓ 完成' : '✗ 未完成'}</div>
        <div>卫星数量: {cesiumState.satelliteList.length}</div>
        <div>当前卫星: {cesiumState.curSatellite || '无'}</div>
        <div>仿真运行: {simulationState.simulationRunning ? '是' : '否'}</div>
        <div>加载状态: {simulationState.isLoading ? '加载中' : '就绪'}</div>
      </div>

      {/* 控制按钮 */}
      <div style={{
        position: 'absolute',
        top: '10px',
        right: '10px',
        zIndex: 1000
      }}>
        <button 
          onClick={() => cesiumState.setIsRotate(!cesiumState.isRotate)}
          style={{
            margin: '5px',
            padding: '8px 12px',
            background: cesiumState.isRotate ? '#4CAF50' : '#f44336',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          地球旋转: {cesiumState.isRotate ? '开' : '关'}
        </button>
      </div>

      {/* 加载动画 */}
      {simulationState.isLoading && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 9999,
          color: 'white',
          fontSize: '18px'
        }}>
          <div style={{
            width: '60px',
            height: '60px',
            border: '4px solid rgba(255, 255, 255, 0.2)',
            borderTop: '4px solid #ffffff',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            marginBottom: '20px'
          }}></div>
          <div>正在加载数据...</div>
          
          <style dangerouslySetInnerHTML={{
            __html: `
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            `
          }} />
        </div>
      )}
    </div>
  );
};

export default TestMain;
